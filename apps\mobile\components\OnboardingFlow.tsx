import React, { useState } from "react";
import { View, ScrollView } from "react-native";
import { router } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { useAuth } from "~/lib/auth-client";
import { PaywallModal } from "./PaywallModal";
import { useFeatureAccess } from "~/hooks/useFeatureAccess";
import { useMutation } from "convex/react";
import { api } from "database";
import { Linking } from "react-native";

interface OnboardingFlowProps {
  onComplete: () => void;
}

export function OnboardingFlow({ onComplete }: OnboardingFlowProps) {
  const { session } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const { paywallVisible, paywallProps, closePaywall, showPaywall } = useFeatureAccess();
  const completeOnboardingMutation = useMutation(api.onboarding.completeOnboarding);

  const onboardingSteps = [
    {
      title: "Welcome to Your Fitness Journey!",
      description: "Get ready to transform your fitness with AI-powered coaching and personalized workouts.",
      content: (
        <View className="space-y-4">
          <Text className="text-center text-muted-foreground">
            We're excited to help you reach your fitness goals with intelligent AI guidance, 
            personalized workout plans, and comprehensive nutrition tracking.
          </Text>
        </View>
      )
    },
    {
      title: "AI-Powered Features",
      description: "Discover what makes our platform special",
      content: (
        <View className="space-y-4">
          <View className="space-y-3">
            <View className="flex-row items-start space-x-3">
              <View className="w-2 h-2 rounded-full bg-primary mt-2" />
              <View className="flex-1">
                <Text className="font-medium">Smart Workout Plans</Text>
                <Text className="text-sm text-muted-foreground">
                  AI creates personalized workouts based on your goals and available equipment
                </Text>
              </View>
            </View>
            <View className="flex-row items-start space-x-3">
              <View className="w-2 h-2 rounded-full bg-primary mt-2" />
              <View className="flex-1">
                <Text className="font-medium">Nutrition Tracking</Text>
                <Text className="text-sm text-muted-foreground">
                  Scan meals and get detailed nutritional insights with AI analysis
                </Text>
              </View>
            </View>
            <View className="flex-row items-start space-x-3">
              <View className="w-2 h-2 rounded-full bg-primary mt-2" />
              <View className="flex-1">
                <Text className="font-medium">Form Guidance</Text>
                <Text className="text-sm text-muted-foreground">
                  Real-time form correction and injury prevention tips
                </Text>
              </View>
            </View>
          </View>
        </View>
      )
    },
    {
      title: "Choose Your Plan",
      description: "Unlock premium features with a 7-day free trial",
      content: (
        <View className="space-y-4">
          <Text className="text-center text-muted-foreground">
            To access all premium features and start your fitness transformation,
            choose a plan that fits your goals.
          </Text>

          {/* Foundation Plan */}
          <Button
            onPress={() => handleSubscriptionSelection('foundation_plan', 'monthly')}
            className="w-full bg-emerald-500"
          >
            <Text className="text-white font-medium">
              Foundation Plan - $9.99/month
            </Text>
          </Button>

          {/* Performance Plan */}
          <Button
            onPress={() => handleSubscriptionSelection('performance_plan', 'monthly')}
            className="w-full bg-amber-500"
          >
            <Text className="text-white font-medium">
              Performance Plan - $19.99/month (Most Popular)
            </Text>
          </Button>

          {/* Champion Plan */}
          <Button
            onPress={() => handleSubscriptionSelection('champion_plan', 'monthly')}
            className="w-full bg-violet-500"
          >
            <Text className="text-white font-medium">
              Champion Plan - $29.99/month
            </Text>
          </Button>

          <Text className="text-center text-xs text-muted-foreground">
            All plans include a 7-day free trial. Cancel anytime.
          </Text>

          <Button
            variant="outline"
            onPress={() => {
              // Skip for now, complete onboarding
              handleCompleteOnboarding();
            }}
            className="w-full"
          >
            <Text className="font-medium">
              Skip for Now
            </Text>
          </Button>
        </View>
      )
    }
  ];

  const handleNext = () => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleCompleteOnboarding();
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubscriptionSelection = async (planId: string, billing: 'monthly' | 'yearly' = 'monthly') => {
    if (!session?.user?.email) {
      console.error('No user session found');
      return;
    }

    try {
      const baseUrl = "http://localhost:3000";

      // Create subscription data to pass in URL
      const subscriptionData = {
        planId,
        billing,
        userEmail: session.user.email,
        timestamp: Date.now(),
        fromOnboarding: true
      };

      // Encode the subscription data
      const encodedData = btoa(JSON.stringify(subscriptionData));

      // Create login URL with subscription data that will redirect to Autumn checkout after login
      const loginUrl = `${baseUrl}/login?from=mobile&subscription_data=${encodedData}&action=subscribe`;

      // Open web app for subscription
      await Linking.openURL(loginUrl);
    } catch (error) {
      console.error('Error opening subscription flow:', error);
      // Fallback to completing onboarding without subscription
      handleCompleteOnboarding();
    }
  };

  const handleCompleteOnboarding = async () => {
    try {
      // Call Convex mutation to mark onboarding as complete
      if (session?.user?.id) {
        await completeOnboardingMutation({ userId: session.user.id });
      }

      onComplete();
      router.replace("/(tabs)/dashboard");
    } catch (error) {
      console.error('Error completing onboarding:', error);
      // Still proceed to dashboard even if mutation fails
      onComplete();
      router.replace("/(tabs)/dashboard");
    }
  };

  const currentStepData = onboardingSteps[currentStep];

  return (
    <View className="flex-1 bg-background">
      <ScrollView className="flex-1 p-6">
        {/* Progress indicator */}
        <View className="flex-row justify-center space-x-2 mb-8">
          {onboardingSteps.map((_, index) => (
            <View
              key={index}
              className={`w-2 h-2 rounded-full ${
                index <= currentStep ? 'bg-primary' : 'bg-muted'
              }`}
            />
          ))}
        </View>

        {/* Main content */}
        <Card className="mb-8">
          <CardHeader className="text-center">
            <CardTitle className="text-xl font-bold">
              {currentStepData.title}
            </CardTitle>
            <Text className="text-muted-foreground">
              {currentStepData.description}
            </Text>
          </CardHeader>
          <CardContent>
            {currentStepData.content}
          </CardContent>
        </Card>
      </ScrollView>

      {/* Navigation buttons */}
      <View className="p-6 border-t border-border">
        <View className="flex-row justify-between space-x-4">
          <Button
            variant="outline"
            onPress={handleBack}
            disabled={currentStep === 0}
            className="flex-1"
          >
            <Text className="font-medium">Back</Text>
          </Button>
          
          {currentStep < onboardingSteps.length - 1 && (
            <Button
              onPress={handleNext}
              className="flex-1 bg-primary"
            >
              <Text className="text-primary-foreground font-medium">Next</Text>
            </Button>
          )}
        </View>
      </View>


    </View>
  );
}
